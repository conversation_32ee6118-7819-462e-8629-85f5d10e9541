import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, FlatList, ActivityIndicator, Alert } from 'react-native';
import { Stack } from 'expo-router';
import { Plus } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { GardenPlantCard } from '@/components/garden/GardenPlantCard';
import { PlantDetailModal } from '@/components/garden/PlantDetailModal';
import { SearchBar } from '@/components/ui/SearchBar';
import { Button } from '@/components/ui/Button';
import { useGarden } from '@/hooks/useGardenStore';
import { GardenPlant } from '@/types/plant';

export default function GardenScreen() {
  const { plants, isLoading, updatePlant, removePlant } = useGarden();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPlant, setSelectedPlant] = useState<GardenPlant | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const filteredPlants = plants.filter(
    (plant) =>
      plant.commonName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plant.scientificName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (plant.nickname && plant.nickname.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handlePlantPress = (plant: GardenPlant) => {
    setSelectedPlant(plant);
    setModalVisible(true);
  };

  // Update selectedPlant when plants array changes (after save)
  useEffect(() => {
    if (selectedPlant && plants.length > 0) {
      const updatedPlant = plants.find(p => p.id === selectedPlant.id);
      if (updatedPlant) {
        setSelectedPlant(updatedPlant);
      }
    }
  }, [plants, selectedPlant?.id]);

  const handlePlantUpdate = async (plantId: string, updates: { nickname?: string; notes?: string; healthStatus?: string; isPublic?: boolean }) => {
    try {
      // Convert healthStatus to health_status for database compatibility
      const dbUpdates = {
        nickname: updates.nickname,
        notes: updates.notes,
        health_status: updates.healthStatus as 'healthy' | 'sick' | 'recovering' | 'critical',
        is_public: updates.isPublic,
      };

      await updatePlant(plantId, dbUpdates);

      // Show success message
      Alert.alert('Success', 'Plant details saved successfully!', [{ text: 'OK' }]);
    } catch (error) {
      console.error('Error updating plant:', error);
      Alert.alert('Error', 'Failed to save plant details. Please try again.');
    }
  };

  const handlePlantRemove = async (plantId: string) => {
    console.log('Garden screen: Starting plant removal confirmation for ID:', plantId);

    // Find the plant to get its name for the confirmation dialog
    const plant = plants.find(p => p.id === plantId);
    const plantName = plant?.nickname || plant?.commonName || 'this plant';

    Alert.alert(
      'Remove Plant',
      `Are you sure you want to remove "${plantName}" from your garden?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            console.log('Garden screen: User confirmed removal, proceeding with deletion');
            try {
              await removePlant(plantId);
              console.log('Garden screen: Plant removal successful');
              // Close modal after successful removal
              setModalVisible(false);
              setSelectedPlant(null);
              Alert.alert('Success', 'Plant removed from garden successfully!');
            } catch (error) {
              console.error('Garden screen: Error removing plant:', error);
              Alert.alert('Error', 'Failed to remove plant. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderItem = ({ item }: { item: GardenPlant }) => (
    <GardenPlantCard
      plant={item}
      onPress={() => handlePlantPress(item)}
    />
  );

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>Your garden is empty</Text>
      <Text style={styles.emptyText}>
        Start by identifying plants and adding them to your garden
      </Text>
      <Button
        title="Identify a Plant"
        onPress={() => {}}
        style={styles.identifyButton}
      />
    </View>
  );

  return (
    <View style={styles.container} testID="garden-screen">
      <Stack.Screen
        options={{
          title: 'My Garden',
          headerRight: () => (
            <Button
              title="Add"
              variant="text"
              style={styles.addButton}
              textStyle={styles.addButtonText}
              onPress={() => {}}
            />
          ),
        }}
      />

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search your garden..."
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredPlants}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          showsVerticalScrollIndicator={false}
          testID="garden-plant-list"
        />
      )}

      <View style={styles.fabContainer}>
        <Button
          title="Add Plant"
          style={styles.fab}
          textStyle={styles.fabText}
          onPress={() => {}}
          testID="add-plant-fab"
        />
      </View>

      <PlantDetailModal
        visible={modalVisible}
        plant={selectedPlant}
        onClose={() => {
          setModalVisible(false);
          setSelectedPlant(null);
        }}
        onUpdate={handlePlantUpdate}
        onRemove={handlePlantRemove}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  addButton: {
    marginRight: 8,
  },
  addButtonText: {
    fontSize: 16,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  identifyButton: {
    width: 200,
  },
  fabContainer: {
    position: 'absolute',
    bottom: 24,
    right: 24,
  },
  fab: {
    borderRadius: 28,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  fabText: {
    marginLeft: 8,
  },
});