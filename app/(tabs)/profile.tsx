import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, Image, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { Stack, router } from 'expo-router';
import { Settings, LogOut, Award, Heart, Camera, Leaf, FileText, Shield, Lock, Eye, EyeOff } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useGarden } from '@/hooks/useGardenStore';
import { useFavorites } from '@/hooks/useFavoritesStore';
import { useAuth } from '@/hooks/useAuth';
import { DatabaseService, UserProfile } from '@/services/database';
import { supabase } from '@/lib/supabase';

export default function ProfileScreen() {
  const { plants } = useGarden();
  const { favorites } = useFavorites();
  const { user, signOut } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isPublicProfile, setIsPublicProfile] = useState(false);

  // Load user profile data
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!user) return;

      try {
        let profile = await DatabaseService.getUserProfile(user.id);

        // If profile doesn't exist, create one
        if (!profile) {
          const newProfile = {
            user_id: user.id,
            username: user.user_metadata?.preferred_username || null,
            display_name: user.user_metadata?.full_name || 'Plant Lover',
            bio: null,
            avatar_url: user.user_metadata?.avatar_url || null,
            location: null,
            website_url: null,
            is_public: false,
            allow_garden_sharing: false,
            allow_profile_indexing: false,
            experience_level: 'beginner' as const,
            total_identifications: 0,
            total_diagnoses: 0,
            community_points: 0,
            achievements: [],
          };

          profile = await DatabaseService.createUserProfile(newProfile);
        } else {
          // Update profile stats to ensure they're current
          profile = await DatabaseService.updateUserProfileStats(user.id) || profile;
        }

        if (profile) {
          setUserProfile(profile);
          setIsPublicProfile(profile.is_public);
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
      }
    };

    loadUserProfile();
  }, [user]);

  // Refresh profile stats when the screen is focused
  useEffect(() => {
    const refreshProfileStats = async () => {
      if (user && userProfile) {
        try {
          const updatedProfile = await DatabaseService.updateUserProfileStats(user.id);
          if (updatedProfile) {
            setUserProfile(updatedProfile);
          }
        } catch (error) {
          console.error('Error refreshing profile stats:', error);
        }
      }
    };

    // Set up an interval to refresh stats periodically
    const interval = setInterval(refreshProfileStats, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [user, userProfile]);

  // User data from auth and profile
  const userData = {
    name: user?.user_metadata?.full_name || userProfile?.display_name || 'Plant Lover',
    email: user?.email || '<EMAIL>',
    avatar: user?.user_metadata?.avatar_url || userProfile?.avatar_url || 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80',
    joinDate: user?.created_at ? new Date(user.created_at) : new Date(),
    identifications: userProfile?.total_identifications || 0,
    diagnoses: userProfile?.total_diagnoses || 0,
    communityPoints: userProfile?.community_points || 0,
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handlePublicProfileToggle = async (value: boolean) => {
    if (!user || !userProfile) return;

    try {
      const updatedProfile = await DatabaseService.updateUserProfile(user.id, {
        is_public: value,
      });

      if (updatedProfile) {
        setUserProfile(updatedProfile);
        setIsPublicProfile(value);
      }
    } catch (error) {
      console.error('Error updating profile visibility:', error);
      Alert.alert('Error', 'Failed to update profile visibility');
    }
  };

  const handleChangePassword = async () => {
    if (!user?.email) {
      Alert.alert('Error', 'Unable to find your email address.');
      return;
    }

    Alert.alert(
      'Change Password',
      'A password reset link will be sent to your email address.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Reset Link',
          onPress: async () => {
            try {
              const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
                redirectTo: 'https://plantidguide.com/reset-password',
              });

              if (error) {
                Alert.alert('Error', 'Failed to send password reset email. Please try again.');
              } else {
                Alert.alert('Success', 'Password reset link sent to your email address.');
              }
            } catch (error) {
              console.error('Error sending password reset:', error);
              Alert.alert('Error', 'Failed to send password reset email. Please try again.');
            }
          },
        },
      ]
    );
  };
  
  return (
    <ScrollView style={styles.container} testID="profile-screen">
      <Stack.Screen 
        options={{ 
          title: 'Profile',
          headerRight: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Settings size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <View style={styles.header}>
        <Image source={{ uri: userData.avatar }} style={styles.avatar} />
        <Text style={styles.name}>{userData.name}</Text>
        <Text style={styles.email}>{userData.email}</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{plants.length}</Text>
            <Text style={styles.statLabel}>Garden</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userData.identifications}</Text>
            <Text style={styles.statLabel}>Identified</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userData.diagnoses}</Text>
            <Text style={styles.statLabel}>Diagnosed</Text>
          </View>
        </View>
      </View>
      


      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Achievements</Text>
        <Card>
          <View style={styles.achievementGrid}>
            {/* Plant Novice Achievement */}
            <View style={styles.achievementItem}>
              <View style={[
                styles.achievementBadge,
                { backgroundColor: userData.identifications >= 10 ? Colors.success : Colors.textMuted }
              ]}>
                <Camera size={20} color={Colors.background} />
              </View>
              <Text style={[
                styles.achievementTitle,
                userData.identifications < 10 && styles.lockedText
              ]}>
                Plant Novice
              </Text>
              <Text style={[
                styles.achievementDesc,
                userData.identifications < 10 && styles.lockedText
              ]}>
                Identify 10+ plants
              </Text>
            </View>

            {/* Plant Expert Achievement */}
            <View style={styles.achievementItem}>
              <View style={[
                styles.achievementBadge,
                { backgroundColor: userData.identifications >= 100 ? Colors.primary : Colors.textMuted }
              ]}>
                <Award size={20} color={Colors.background} />
              </View>
              <Text style={[
                styles.achievementTitle,
                userData.identifications < 100 && styles.lockedText
              ]}>
                Plant Expert
              </Text>
              <Text style={[
                styles.achievementDesc,
                userData.identifications < 100 && styles.lockedText
              ]}>
                Identify 100+ plants
              </Text>
            </View>

            {/* Plant Guru Achievement */}
            <View style={styles.achievementItem}>
              <View style={[
                styles.achievementBadge,
                { backgroundColor: userData.identifications >= 300 ? Colors.warning : Colors.textMuted }
              ]}>
                <Leaf size={20} color={Colors.background} />
              </View>
              <Text style={[
                styles.achievementTitle,
                userData.identifications < 300 && styles.lockedText
              ]}>
                Plant Guru
              </Text>
              <Text style={[
                styles.achievementDesc,
                userData.identifications < 300 && styles.lockedText
              ]}>
                Identify 300+ plants
              </Text>
            </View>
          </View>
        </Card>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        <Card style={styles.accountCard}>
          <TouchableOpacity style={styles.accountOption} onPress={handleChangePassword}>
            <Lock size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Change Password</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <View style={styles.accountOption}>
            <View style={styles.accountLeft}>
              {isPublicProfile ? (
                <Eye size={20} color={Colors.primary} style={styles.accountIcon} />
              ) : (
                <EyeOff size={20} color={Colors.primary} style={styles.accountIcon} />
              )}
              <Text style={styles.accountOptionText}>Public Profile</Text>
            </View>
            <Switch
              value={isPublicProfile}
              onValueChange={handlePublicProfileToggle}
              trackColor={{ false: Colors.border, true: Colors.primary }}
              thumbColor={Colors.background}
            />
          </View>

          <View style={styles.accountDivider} />

          <TouchableOpacity
            style={styles.accountOption}
            onPress={() => router.push('/legal/terms')}
          >
            <FileText size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Terms of Service</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity
            style={styles.accountOption}
            onPress={() => router.push('/legal/privacy')}
          >
            <Shield size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Privacy Policy</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity style={styles.accountOption} onPress={handleSignOut}>
            <LogOut size={20} color={Colors.error} style={styles.accountIcon} />
            <Text style={[styles.accountOptionText, { color: Colors.error }]}>Sign Out</Text>
          </TouchableOpacity>
        </Card>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>PlantIDGuide v1.0.0</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
    marginRight: 8,
  },
  header: {
    alignItems: 'center',
    padding: 20,
    paddingBottom: 0,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: Colors.textLight,
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.textLight,
  },
  statDivider: {
    width: 1,
    height: '100%',
    backgroundColor: Colors.border,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  lockedText: {
    color: Colors.textMuted,
  },
  accountCard: {
    padding: 0,
  },
  settingsCard: {
    padding: 0,
  },
  accountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  accountIcon: {
    marginRight: 16,
  },
  accountOptionText: {
    fontSize: 16,
    color: Colors.text,
  },
  accountDivider: {
    height: 1,
    backgroundColor: Colors.border,
    width: '100%',
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  settingDivider: {
    height: 1,
    backgroundColor: Colors.border,
    marginLeft: 32,
  },
  achievementGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  achievementItem: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    padding: 12,
  },
  achievementBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  achievementTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  achievementDesc: {
    fontSize: 10,
    color: Colors.textMuted,
    textAlign: 'center',
    lineHeight: 14,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: Colors.textMuted,
  },
});